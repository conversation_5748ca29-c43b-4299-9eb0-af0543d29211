<div class="ai-chat-container">
    <!-- Chat <PERSON> -->
    <div class="ai-chat-button">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
        </svg>
    </div>

    <!-- Chat Window -->
    <div class="ai-chat-window">
        <!-- Chat Header -->
        <div class="ai-chat-header">
            <h3>DigiAI Assistant</h3>
            <div class="ai-chat-header-actions">
                <button class="ai-chat-clear-button ai-chat-header-button" title="Clear conversation">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="ai-chat-messages">
            <!-- Messages will be added here dynamically -->
        </div>

        <!-- Chat Input -->
        <div class="ai-chat-input-container">
            <input type="text" class="ai-chat-input" placeholder="Type your message...">
            <button class="ai-chat-send-button">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
            </button>
        </div>

        <!-- Login Overlay (shown for non-authenticated users) -->
        <div class="ai-chat-login-overlay" style="display: none;">
            <div class="ai-chat-login-message">
                Please log in to chat with our AI assistant
            </div>
            <button class="ai-chat-login-button">
                Log In
            </button>
        </div>
    </div>
</div>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/components/ai-chat.blade.php ENDPATH**/ ?>